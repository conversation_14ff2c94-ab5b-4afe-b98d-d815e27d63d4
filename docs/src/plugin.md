# Plugins

Plugins are created by the community to extend the functionality of Comtrya. They allow users to customize and enhance their experience by adding new features or modifying existing ones.

Plugins can be used to automate tasks, integrate with other tools, or provide additional configuration options.

> [!IMPORTANT]
> Plugins are extremely powerful and can significantly alter the behavior of your system.
> Use them with caution and ensure you understand the implications of the changes they introduce.
> When using plugins, it's important to review their documentation and understand the changes they will make to your system.
> Always test plugins in a safe environment before deploying them in production to ensure they work as expected and do
> not introduce any unwanted side effects.

```yaml
actions:
  - action: plugin
    repo: username/repo
    version: *
    opts:
      - action-name:
        key1: value1
        key2: value2
```

## Fields

[!NOTE] Plugins can be loaded from a remote repository or locally. Only one location can be used per action. (i.e `dir` and `repo` are exclusive (i.e cannot be used together)

- **dir**: The path to the plugin directory. (Aliases: `directory`, `path`)
- **repository**: The repository in the format `username/repo`. (Alias: `repo`)
  - **version**: The version of the plugin. (Optional)
    - *`stable`:* Use the most recent release. (Default)
    - *`latest`:* Use the most recent commit. (<PERSON><PERSON>: `*`)
    - *`version`:* Use a release tag from the repository.
      - Plugin actions can use different versions of the same plugin, but only one version per action.
- **actions**: A list of options for the plugin. (Alias: `acts`)
  - **Plugin specific!**: Refer to the plugin's documentation for available actions and options.

## Examples

### Example 1: Using a Stable Version

```yaml
plugins:
  - source:
    repo: example_user/example_plugin
    version: stable # stable or omit for latest release
    actions:
      - configure:
        setting: default
```

### Example 2: Using the Latest Version

```yaml
plugins:
  - source:
      repo: another_user/another_plugin
      version: latest # latest or *
    options:
      - initialize:
        mode: fast
```

### Example 3: Using a Tagged Version

```yaml
plugins:
  - source:
      repo: some_user/some_plugin
      version: v1.2.3 # must match tag or may not work
    options:
      - deploy:
        environment: production
```

## Additional Notes


