# How to contribute

Thanks for reading this document!

## Prerequisites
- Rust (e.g. via `rustup install stable`)
- Cargo (with `fmt`, `clippy` and `nextest`):
  - `rustup component add rustfmt`
  - `rustup component add clippy`
  - `cargo install cargo-nextest`

## Code style
Before you create a PR, just run `check.sh`:
- This will format your code via `fmt`
- Runs the linter `clippy`
- Runs all tests via `nextest`

## Good first issues
If you want to contribute, but don't feel ready for the big tasks, feel free to choose from one of our [good first issues](https://github.com/comtrya/comtrya/issues?q=is%3Aissue+is%3Aopen+label%3AMeta%3A%3AGoodFirstIssue)

### Guides
If you're lost or not know how to start, you can contact:
- @rawkode
- @icepuma
- @martintc

at the [Rawkode Academy Discord](https://rawkode.chat/) in the `#comtrya` channel.

## Pull requests
We provide a [pull request template](https://github.com/comtrya/comtrya/blob/main/.github/pull_request_template.md) to guide you through the progress.

## Help
If you want to get in touch with us, join the [Rawkode Academy Discord](https://rawkode.chat/) server and jump into the `#comtrya` channel.

> See You Space Cowboy...
