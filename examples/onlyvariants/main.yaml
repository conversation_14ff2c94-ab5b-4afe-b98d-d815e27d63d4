actions:
  # This action has a "default" execution for when the variants don't overlay,
  # as it does not provide its own "where"
  - action: command.run
    command: echo
    args:
      - hello, world!
    variants:
      - where: os.name == "linux"
        command: echo
        args: ["Hello", "Linux"]
      - where: os.name == "macos"
        command: echo
        args: ["Hello", "macOS"]
      - where: user.username == "rawkode"
        command: echo
        args: ["Hello", "rawkode!"]

  # This action has NO "default" execution for when the variants don't overlay,
  # as it does provide its own "where"
  - action: command.run
    command: echo
    args:
      - hello, world!
    where: os.name == "linux"
