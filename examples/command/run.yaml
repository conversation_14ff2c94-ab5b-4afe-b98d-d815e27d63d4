actions:
  # Action ID can be command.run or cmd.run
  - action: command.run
    command: echo
    args:
      - hi

  - action: command.run
    command: whoami
    privileged: true

  - action: command.run
    command: ls
    args:
      - "{{ user.home_dir }}"

  - action: command.run
    command: sh
    args:
      - "-c"
      - "curl -sfL https://get.k3s.io | sh -"
    dir: .
    privileged: true

  # we should see the gobin set in the go env output now
  - action: command.run
    command: go
    args:
      - env
    env:
      GOBIN: /Users/<USER>

  - action: command.run
    command: go
    args:
      - env
