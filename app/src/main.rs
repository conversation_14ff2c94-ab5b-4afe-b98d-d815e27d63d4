use crate::commands::ComtryaCommand;
use crate::config::{Commands, GlobalArgs};

use std::io;
use std::path::PathBuf;
use std::sync::{Arc, Mutex};

use comtrya_lib::contexts::build_contexts;

use comtrya_lib::manifests;
use comtrya_lib::runtime::{set_elevated_pipe, ElevatedPipe, Runtime};

use clap::Parser;
use tracing::{debug, error, Level};

#[allow(unused_imports)]
use tracing_subscriber::{fmt::writer::MakeWriterExt, layer::SubscriberExt, FmtSubscriber};

mod commands;
mod config;


#[derive(Debug)]
pub struct AppRuntime {
    pub(crate) args: GlobalArgs,
    pub(crate) runtime: Runtime,
}

impl AppRuntime {
    /// Checks if any actions in any manifests require elevated privileges
    pub fn requires_privileges(&self) -> bool {
        use comtrya_lib::manifests::load;
        use tracing::debug;

        // Get the manifest path using the same logic as Apply command
        let Ok(manifest_path) = self.get_manifest_path() else {
            debug!("Failed to get manifest path, assuming no privileges needed");
            return false;
        };
        

        debug!("Checking for privilege requirements in manifests at: {:?}", manifest_path);

        // Load manifests
        let manifests = load(manifest_path, &self.runtime.contexts);

        // Check each manifest and its actions
        for (name, manifest) in manifests.iter() {
            debug!("Checking manifest: {}", name);

            for action in manifest.actions.iter() {
                let action = action.inner_ref();

                // Plan the action to get the steps
                match action.plan(manifest, &self.runtime) {
                    Ok(steps) => {
                        for step in steps {
                            // Check if any atom requires privileges
                            if self.step_requires_privileges(&step) {
                                debug!("Found privileged step in manifest '{}': {}", name, step.atom);
                                return true;
                            }
                        }
                    }
                    Err(err) => {
                        debug!("Failed to plan action in manifest '{}': {:?}", name, err);
                        // Continue checking other actions
                    }
                }
            }
        }

        debug!("No privileged actions found");
        false
    }

    /// Helper method to get manifest path (similar to Apply::manifest_path)
    fn get_manifest_path(&self) -> anyhow::Result<PathBuf> {
        use std::path::PathBuf;

        if let Some(manifest_directory) = &self.args.manifest_directory {
            Ok(PathBuf::from(manifest_directory))
        } else if !self.runtime.config.manifest_paths.is_empty() {
            Ok(PathBuf::from(&self.runtime.config.manifest_paths[0]))
        } else {
            Ok(PathBuf::from("./"))
        }
    }

    /// Helper method to check if a step requires privileges
    fn step_requires_privileges(&self, step: &comtrya_lib::steps::Step) -> bool {
        use comtrya_lib::atoms::command::Exec;

        // Check if the atom is an Exec atom and if it's privileged
        if let Some(exec) = step.atom.as_any().downcast_ref::<Exec>() {
            return exec.privileged;
        }

        // For other atom types, we assume they don't require privileges
        // This can be extended in the future if other atoms support privilege escalation
        false
    }
}

/// Simple privilege detection using the privilege crate
fn is_elevated() -> bool {
    privilege::user::privileged()
}

fn start_elevated_helper() -> anyhow::Result<(String, ElevatedPipe)> {
    use anyhow::anyhow;
    use std::process::Command;

    let current_exe = std::env::current_exe()?;
    let pipe_name = format!("comtrya_elevated_{}", std::process::id());

    debug!("Starting elevated helper with pipe: {}", pipe_name);

    // Try different privilege escalation tools in order of preference
    let tools = ["sudo", "doas", "run0"];

    for tool in &tools {
        if comtrya_lib::utilities::get_binary_path(tool).is_ok() {
            debug!("Using privilege escalation tool: {}", tool);

            let _child = Command::new(tool)
                .arg(&current_exe)
                .arg("elevated-helper")
                .arg("--pipe-name")
                .arg(&pipe_name)
                .spawn()
                .map_err(|e| anyhow!("Failed to start elevated helper with {}: {}", tool, e))?;

            // Give the helper a moment to start up
            std::thread::sleep(std::time::Duration::from_millis(500));

            // Create the shared pipe connection - initially None, will be connected on first use
            let elevated_pipe = Arc::new(Mutex::new(None));

            debug!("Elevated helper started successfully with {}", tool);
            return Ok((pipe_name, elevated_pipe));
        }
    }

    Err(anyhow!("No privilege escalation tool found (sudo, doas, run0)"))
}

pub(crate) fn execute(runtime: AppRuntime) -> anyhow::Result<()> {
    match &runtime.args.command {
        Commands::Apply(apply) => apply.execute(&runtime),
        Commands::Status(apply) => apply.status(&runtime),
        Commands::Version(version) => version.execute(&runtime),
        Commands::Contexts(contexts) => contexts.execute(&runtime),
        Commands::GenCompletions(gen_completions) => gen_completions.execute(&runtime),
        Commands::ElevatedHelper(elevated_helper) => elevated_helper.execute(&runtime),
    }
}

fn configure_tracing(args: &GlobalArgs) {
    let stdout_writer = match args.verbose {
        0 => io::stdout.with_max_level(tracing::Level::INFO),
        1 => io::stdout.with_max_level(tracing::Level::DEBUG),
        _ => io::stdout.with_max_level(tracing::Level::TRACE),
    };

    let builder = FmtSubscriber::builder()
        .with_max_level(Level::TRACE)
        .with_ansi(!args.no_color)
        .with_target(false)
        .with_writer(stdout_writer)
        .without_time();

    #[cfg(target_os = "linux")]
    if let Ok(layer) = tracing_journald::layer() {
        tracing::subscriber::set_global_default(builder.finish().with(layer))
            .expect("Unable to set a global subscriber");
        return;
    }

    tracing::subscriber::set_global_default(builder.finish())
        .expect("Unable to set a global subscriber");
}

fn main() -> anyhow::Result<()> {
    let args = GlobalArgs::parse();
    configure_tracing(&args);

    let config = match config::load_config(&args) {
        Ok(config) => config,
        Err(error) => {
            error!("{}", error.to_string());
            panic!();
        }
    };

    if !config.disable_update_check {
        check_for_updates(args.no_color);
    }

    // Run Context Providers
    let contexts = build_contexts(&config);

    let runtime = AppRuntime {
        args,
        runtime: Runtime::new(config, contexts),
    };

    // Check if we need elevated privileges and start helper if necessary
    if runtime.requires_privileges() && !is_elevated() {
        debug!("Privileges required but not elevated, starting elevated helper");
        let (pipe_name, helper_pipe) = start_elevated_helper()?;
        // Set the environment variable for backward compatibility
        std::env::set_var("COMTRYA_ELEVATED_PIPE", &pipe_name);
        // Set the global elevated pipe
        set_elevated_pipe(helper_pipe);
    }

    execute(runtime)?;

    Ok(())
}

fn check_for_updates(no_color: bool) {
    use colored::*;
    use update_informer::{registry, Check};

    if no_color {
        control::set_override(false);
    }

    let pkg_name = env!("CARGO_PKG_NAME");
    let pkg_version = env!("CARGO_PKG_VERSION");
    let informer = update_informer::new(registry::Crates, pkg_name, pkg_version);

    if let Some(new_version) = informer.check_version().ok().flatten() {
        let msg = format!(
            "A new version of {pkg_name} is available: v{pkg_version} -> {new_version}",
            pkg_name = pkg_name.italic().cyan(),
            new_version = new_version.to_string().green()
        );

        let release_url =
            format!("https://github.com/{pkg_name}/{pkg_name}/releases/tag/{new_version}").blue();
        let changelog = format!("Changelog: {release_url}",);

        let cmd = format!(
            "Run to update: {cmd}",
            cmd = "curl -fsSL https://get.comtrya.dev | sh".green()
        );

        println!("\n{msg}\n{changelog}\n{cmd}");
    }
}
