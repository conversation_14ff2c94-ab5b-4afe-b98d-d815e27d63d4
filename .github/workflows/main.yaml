name: release

on:
  workflow_dispatch: {}
  push:
    branches:
      - "main"

env:
  MIN_SUPPORTED_RUST_VERSION: "1.86.0"
  CICD_INTERMEDIATES_DIR: "_cicd-intermediates"
  CARGO_TERM_COLOR: always
  #Fix these up soon
  #RUSTFLAGS: "-D warnings"

jobs:
  lint-test-release:
    runs-on: ubuntu-22.04
    steps:
      - name: Clone Repository
        uses: actions/checkout@v4

      - name: Install Rust Toolchain (v${{ env.MIN_SUPPORTED_RUST_VERSION }})
        uses: actions-rs/toolchain@v1
        with:
          toolchain: ${{ env.MIN_SUPPORTED_RUST_VERSION }}
          default: true
          profile: minimal
          components: clippy

      - uses: actions-rs/clippy-check@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          args: --all-features --all-targets

      - name: Run Tests
        uses: actions-rs/cargo@v1
        with:
          command: test

      - name: Upload to codecov.io
        uses: codecov/codecov-action@v5

      - name: Check for Release
        id: is-release
        shell: bash
        run: |
          unset IS_RELEASE ; if [[ $GITHUB_REF =~ ^refs/tags/v[0-9].* ]]; then IS_RELEASE='true' ; fi
          echo ::set-output name=IS_RELEASE::${IS_RELEASE}

      - name: Cargo Publish "comtrya-lib"
        if: steps.is-release.outputs.IS_RELEASE
        run: cargo publish -p comtrya-lib --token ${CRATES_TOKEN}
        env:
          CRATES_TOKEN: ${{ secrets.CARGO_REGISTRY_TOKEN }}

      - name: Cargo Publish "comtrya"
        if: steps.is-release.outputs.IS_RELEASE
        run: cargo publish -p comtrya --token ${CRATES_TOKEN}
        env:
          CRATES_TOKEN: ${{ secrets.CARGO_REGISTRY_TOKEN }}

  build:
    name: Build-${{ matrix.platform.target }}
    runs-on: ${{ matrix.platform.os }}
    strategy:
      fail-fast: false
      matrix:
        platform:
          - os_name: Linux-x86_64-gnu
            os: ubuntu-24.04
            target: x86_64-unknown-linux-gnu
            bin: comtrya
            name: comtrya-x86_64-unknown-linux-gnu
            cross: false
            cargo_command: cargo
            skip_tests: false
          - os_name: Linux-aarch64-gnu
            os: ubuntu-24.04
            target: aarch64-unknown-linux-gnu
            bin: comtrya
            name: comtrya-aarch64-unknown-linux-gnu
            cross: false
            cargo_command: ./cross
            skip_tests: false
          - os_name: Windows-x86_64
            os: windows-2022
            target: x86_64-pc-windows-msvc
            bin: comtrya.exe
            name: comtrya-x86_64-pc-windows-msvc
            cross: false
            cargo_command: cargo
            skip_tests: false
          - os_name: macOS-x86_64
            os: macos-13
            target: x86_64-apple-darwin
            bin: comtrya
            name: comtrya-x86_64-apple-darwin
            cross: false
            cargo_command: cargo
            skip_tests: false
          - os_name: macOS-aarch64
            os: macos-15
            target: aarch64-apple-darwin
            bin: comtrya
            name: comtrya-aarch64-apple-darwin
            cross: false
            cargo_command: ./cross
            skip_tests: false
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Build binary
        uses: houseabsolute/actions-rust-cross@v1
        with:
          command: "build"
          target: ${{ matrix.platform.target }}
          toolchain: stable
          args: "--release"

      - name: Rename Binary
        if: matrix.platform.target != 'x86_64-pc-windows-msvc'
        shell: bash
        run: |
          mv target/${{ matrix.platform.target }}/release/comtrya comtrya-${{ matrix.platform.target }}

      - name: Rename Binary.exe
        if: matrix.platform.target == 'x86_64-pc-windows-msvc'
        shell: bash
        run: |
          mv target/${{ matrix.platform.target }}/release/comtrya.exe comtrya-${{ matrix.platform.target }}.exe

      - name: Upload Artifact
        if: matrix.platform.target != 'x86_64-pc-windows-msvc'
        uses: softprops/action-gh-release@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          draft: true
          prerelease: false
          name: Unreleased
          generate_release_notes: false
          fail_on_unmatched_files: true
          files: |
            comtrya-${{ matrix.platform.target }}

      - name: Upload Artifact.exe
        if: matrix.platform.target == 'x86_64-pc-windows-msvc'
        uses: softprops/action-gh-release@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          draft: true
          prerelease: false
          name: Unreleased
          generate_release_notes: false
          fail_on_unmatched_files: true
          files: |
            comtrya-${{ matrix.platform.target }}.exe

  build-musl:
    runs-on: ubuntu-latest
    container: clux/muslrust:stable
    steps:
      - name: Clone Repository
        uses: actions/checkout@v4

      - name: Link to muscl Toolchain
        run: |
          ln -s /root/.cargo $HOME/.cargo
          ln -s /root/.rustup $HOME/.rustup

      - name: Handle Rust Dependencies Caching
        uses: Swatinem/rust-cache@v2
        with:
          key: v1-linux-musl

      - name: Build Release Binary
        uses: actions-rs/cargo@v1
        with:
          command: build
          args: --release

      - name: Rename Binary
        shell: bash
        run: |
          mv target/x86_64-unknown-linux-musl/release/comtrya target/x86_64-unknown-linux-musl/release/comtrya-x86_64-unknown-linux-musl

      - name: Upload release archive
        uses: softprops/action-gh-release@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          draft: true
          prerelease: false
          name: Unreleased
          generate_release_notes: false
          fail_on_unmatched_files: true
          files: |
            target/x86_64-unknown-linux-musl/release/comtrya-x86_64-unknown-linux-musl
