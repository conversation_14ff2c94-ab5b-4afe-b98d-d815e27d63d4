name: pull-request

env:
  MIN_SUPPORTED_RUST_VERSION: "1.77.0"

on:
  pull_request:
    branches:
      - main

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: agenthunt/conventional-commit-checker-action@v2.0.0
        continue-on-error: true
        with:
          pr-body-regex: ".*"

      - uses: actions-rs/toolchain@v1
        with:
          toolchain: ${{ env.MIN_SUPPORTED_RUST_VERSION }}
          profile: minimal
          components: clippy,rustfmt
          override: true

      - uses: actions-rs/cargo@v1
        with:
          command: fmt
          args: --all -- --check

      - uses: giraffate/clippy-action@v1
        with:
          reporter: "github-pr-review"
          github_token: ${{ secrets.GITHUB_TOKEN }}
          clippy_flags: --all-features --all-targets

      - uses: codecov/codecov-action@v5

  test:
    name: ${{ matrix.job.os }} (${{ matrix.job.target }})
    runs-on: ${{ matrix.job.os }}
    strategy:
      fail-fast: false
      matrix:
        job:
          # Some jobs are commented out. <PERSON> currently runs
          # the build in a container, which doesn't have a "proper"
          # setup that allows CACHE_DIR / TMP_DIR etc to exist,
          # meaning that some of the tests pass because it's using `/`
          # and the user isn't root. There's a change to Cross that fixes
          # this, but a version hasn't been released in 8 months.
          # Hopefully we can uncomment these soon.
          # - {
          #     os: ubuntu-20.04,
          #     target: aarch64-unknown-linux-gnu,
          #     use-cross: true,
          #   }
          - { os: ubuntu-24.04, target: x86_64-unknown-linux-gnu }
          # - {
          #     os: ubuntu-20.04,
          #     target: x86_64-unknown-linux-musl,
          #     use-cross: true,
          #   }
          - { os: macos-13, target: x86_64-apple-darwin }
          - { os: macos-14, target: aarch64-apple-darwin }
          - { os: windows-2022, target: x86_64-pc-windows-msvc }
    steps:
      - uses: actions/checkout@v4

      - shell: bash
        run: |
          case ${{ matrix.job.target }} in
            aarch64-unknown-linux-gnu) sudo apt-get -y update ; sudo apt-get -y install gcc-aarch64-linux-gnu ;;
          esac

      - shell: bash
        run: |
          echo "PROJECT_NAME=$(sed -n 's/^name = "\(.*\)"/\1/p' Cargo.toml)" >> $GITHUB_ENV
          echo "PROJECT_VERSION=$(sed -n 's/^version = "\(.*\)"/\1/p' Cargo.toml | head -n1)" >> $GITHUB_ENV
          echo "PROJECT_MAINTAINER=$(sed -n 's/^authors = \["\(.*\)"\]/\1/p' Cargo.toml)" >> $GITHUB_ENV
          echo "PROJECT_HOMEPAGE=$(sed -n 's/^homepage = "\(.*\)"/\1/p' Cargo.toml)" >> $GITHUB_ENV

      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          target: ${{ matrix.job.target }}
          override: true
          profile: minimal

      - uses: actions-rs/cargo@v1
        with:
          use-cross: ${{ matrix.job.use-cross }}
          command: test
          args: --target=${{ matrix.job.target }} ${{ steps.test-options.outputs.CARGO_TEST_OPTIONS}}
