use crate::atoms::directory::Create as DirectoryCreateAtom;
use crate::manifests::Manifest;
use crate::steps::Step;
use crate::{actions::Action, contexts::Contexts};
use schemars::JsonSchema;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

#[derive(<PERSON>son<PERSON>che<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ial<PERSON>q, Eq, Serialize, Deserialize)]
pub struct DirectoryCreate {
    pub path: String,
}

impl Action for DirectoryCreate {
    fn summarize(&self) -> String {
        format!("Creating directory {}", self.path)
    }

    fn plan(&self, _: &Manifest, _runtime: &crate::runtime::Runtime) -> anyhow::Result<Vec<Step>> {
        Ok(vec![Step {
            atom: Box::new(DirectoryCreateAtom {
                path: PathBuf::from(&self.path),
            }),
            initializers: vec![],
            finalizers: vec![],
        }])
    }
}

#[cfg(test)]
mod tests {
    use crate::actions::Actions;
    use crate::manifests::Manifest;
    use std::path::Path<PERSON>uf;

    fn get_manifest_dir() -> PathBuf {
        std::env::current_dir()
            .unwrap()
            .parent()
            .unwrap()
            .join("examples")
            .join("directory")
    }

    #[test]
    fn it_can_be_deserialized() {
        let example_yaml = std::fs::File::open(get_manifest_dir().join("create.yaml")).unwrap();
        let mut manifest: Manifest = serde_yml::from_reader(example_yaml).unwrap();

        match manifest.actions.pop() {
            Some(Actions::DirectoryCreate(action)) => {
                assert_eq!("/some-directory", action.action.path);
            }
            _ => {
                panic!("DirectoryCopy didn't deserialize to the correct type");
            }
        };
    }
}
