use std::sync::{<PERSON>, <PERSON>tex};
use crate::contexts::Contexts;

/// Shared elevated pipe connection for privilege escalation
pub type ElevatedPipe = Arc<Mutex<Option<interprocess::local_socket::Stream>>>;

/// Global registry for the elevated pipe connection
static ELEVATED_PIPE_REGISTRY: std::sync::OnceLock<ElevatedPipe> = std::sync::OnceLock::new();

/// Get the global elevated pipe connection
pub fn get_elevated_pipe() -> Option<ElevatedPipe> {
    ELEVATED_PIPE_REGISTRY.get().cloned()
}

/// Set the global elevated pipe connection
pub fn set_elevated_pipe(pipe: ElevatedPipe) {
    let _ = ELEVATED_PIPE_REGISTRY.set(pipe);
}

// Re-export types that will be needed by the app
pub use crate::config::Config;

/// Runtime contains all the configuration and context needed to execute Comtrya
/// This is a simple struct that contains only the essential data
#[derive(Debug, Default)]
pub struct Runtime {
    pub config: Config,
    pub contexts: Contexts,
}

impl Runtime {
    /// Create a new Runtime instance
    pub fn new(config: Config, contexts: Contexts) -> Self {
        Self {
            config,
            contexts,
        }
    }

    /// Get the contexts
    pub fn contexts(&self) -> &Contexts {
        &self.contexts
    }

    /// Get the config
    pub fn config(&self) -> &Config {
        &self.config
    }
}
